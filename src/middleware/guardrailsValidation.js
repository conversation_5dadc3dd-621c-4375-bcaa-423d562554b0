const fetch = require('node-fetch');

/**
 * Guardrails validation middleware
 * Validates user input before processing
 */
class GuardrailsValidationMiddleware {
    constructor() {
        this.guardrailsServiceUrl = process.env.GUARDRAILS_SERVICE_URL || 'http://localhost:8001';
        this.enabled = process.env.GUARDRAILS_ENABLED === 'true';
        this.validationType = process.env.GUARDRAILS_VALIDATION_TYPE || 'profanity';
        this.model = process.env.GUARDRAILS_MODEL || 'mistralai/mistral-nemo:free';  // Match main service model
        this.timeout = parseInt(process.env.GUARDRAILS_TIMEOUT) || 5000;
        this.apiKey = process.env.GUARDRAILS_API_KEY || 'guardrails-secret-key-2024';
    }

    /**
     * Get authentication headers
     * @returns {Object} Headers with API key
     */
    getAuthHeaders() {
        return {
            'Content-Type': 'application/json',
            'X-API-Key': this.apiKey
        };
    }

    /**
     * Validate content using Guardrails service
     * @param {string} content - Content to validate
     * @returns {Promise<Object>} Validation result
     */
    async validateContent(content) {
        if (!this.enabled) {
            return {
                is_valid: true,
                validated_content: content,
                validation_errors: [],
                bypassed: true
            };
        }

        // Log request parameters being sent to guardrails service
        const requestPayload = {
            content,
            model: this.model,
            validation_type: this.validationType
        };

        console.log('🚀 Guardrails Service Request:', {
            url: `${this.guardrailsServiceUrl}/validate`,
            method: 'POST',
            headers: this.getAuthHeaders(),
            payload: requestPayload
        });

        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/validate`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(requestPayload),
                timeout: this.timeout
            });

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Guardrails service authentication failed - check API key');
                } else if (response.status === 403) {
                    throw new Error('Guardrails service access denied - invalid API key');
                } else {
                    throw new Error(`Guardrails service error: ${response.status} ${response.statusText}`);
                }
            }

            const result = await response.json();

            // Log response received from guardrails service
            console.log('✅ Guardrails Service Response:', {
                status: response.status,
                statusText: response.statusText,
                result: result
            });

            return result;
        } catch (error) {
            console.error('❌ Guardrails validation failed:', error.message);

            // Fail safe - if validation service is down, allow content but log warning
            return {
                is_valid: true,
                validated_content: content,
                validation_errors: [`Validation service unavailable: ${error.message}`],
                bypassed: true
            };
        }
    }

    /**
     * Validate multiple content items in batch
     * @param {Array<string>} contents - Array of content to validate
     * @returns {Promise<Object>} Batch validation result
     */
    async validateBatchContent(contents) {
        if (!this.enabled) {
            return {
                results: contents.map(content => ({
                    original_content: content,
                    is_valid: true,
                    validated_content: content,
                    validation_errors: [],
                    bypassed: true
                })),
                total_processed: contents.length,
                bypassed: true
            };
        }

        // Log batch request parameters being sent to guardrails service
        const batchRequestPayload = {
            contents,
            model: this.model,
            validation_type: this.validationType
        };

        console.log('🚀 Guardrails Batch Service Request:', {
            url: `${this.guardrailsServiceUrl}/validate/batch`,
            method: 'POST',
            headers: this.getAuthHeaders(),
            payload: batchRequestPayload
        });

        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/validate/batch`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(batchRequestPayload),
                timeout: this.timeout
            });

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('Guardrails service authentication failed - check API key');
                } else if (response.status === 403) {
                    throw new Error('Guardrails service access denied - invalid API key');
                } else {
                    throw new Error(`Guardrails service error: ${response.status} ${response.statusText}`);
                }
            }

            const result = await response.json();

            // Log batch response received from guardrails service
            console.log('✅ Guardrails Batch Service Response:', {
                status: response.status,
                statusText: response.statusText,
                result: result
            });

            return result;
        } catch (error) {
            console.error('❌ Guardrails batch validation failed:', error.message);

            // Fail safe - if validation service is down, allow all content but log warning
            return {
                results: contents.map(content => ({
                    original_content: content,
                    is_valid: true,
                    validated_content: content,
                    validation_errors: [`Validation service unavailable: ${error.message}`],
                    bypassed: true
                })),
                total_processed: contents.length,
                bypassed: true
            };
        }
    }

    /**
     * Express middleware for validating request body
     */
    validateRequest() {
        return async (req, res, next) => {
            try {
                // Skip validation for non-POST requests
                if (req.method !== 'POST') {
                    return next();
                }

                // Extract content from request body
                const content = req.body.query || req.body.message || req.body.content;

                if (!content) {
                    return next();
                }

                // Validate the content
                const validationResult = await this.validateContent(content);

                // Log validation result
                if (!validationResult.is_valid) {
                    console.warn('Content validation failed:', {
                        content: content.substring(0, 100) + '...',
                        errors: validationResult.validation_errors,
                        userAgent: req.get('User-Agent'),
                        ip: req.ip
                    });
                }

                // Always replace content with validated content (even if it's sanitized)
                if (validationResult.validated_content !== content) {
                    if (req.body.query) req.body.query = validationResult.validated_content;
                    if (req.body.message) req.body.message = validationResult.validated_content;
                    if (req.body.content) req.body.content = validationResult.validated_content;

                    console.log('Content sanitized by Guardrails:', {
                        original: content.substring(0, 50) + '...',
                        sanitized: validationResult.validated_content.substring(0, 50) + '...'
                    });
                }

                // Only reject if validation failed AND we have no sanitized content
                if (!validationResult.is_valid && !validationResult.bypassed && validationResult.validated_content === content) {
                    return res.status(400).json({
                        error: true,
                        message: 'Content validation failed',
                        validation_errors: validationResult.validation_errors,
                        validated_content: validationResult.validated_content
                    });
                }

                // Add validation metadata to request
                req.guardrailsValidation = {
                    is_valid: validationResult.is_valid,
                    validation_errors: validationResult.validation_errors,
                    bypassed: validationResult.bypassed || false,
                    confidence_score: validationResult.confidence_score,
                    metadata: validationResult.metadata,
                    original_content: content,
                    sanitized_content: validationResult.validated_content
                };

                next();
            } catch (error) {
                console.error('Guardrails middleware error:', error);
                // Continue processing even if validation fails
                next();
            }
        };
    }

    /**
     * Check if Guardrails service is healthy
     * @returns {Promise<boolean>} Health status
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/health`, {
                method: 'GET',
                timeout: 3000
            });

            return response.ok;
        } catch (error) {
            console.error('Guardrails health check failed:', error.message);
            return false;
        }
    }

    /**
     * Get available validators
     * @returns {Promise<Array>} List of available validators
     */
    async getValidators() {
        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/validators`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                timeout: 3000
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    throw new Error('Authentication failed - check API key');
                }
                throw new Error(`Guardrails service error: ${response.status}`);
            }

            const result = await response.json();
            return result.validators || [];
        } catch (error) {
            console.error('Failed to get validators:', error.message);
            return [];
        }
    }

    /**
     * Get Guardrails service configuration
     * @returns {Promise<Object>} Configuration info
     */
    async getConfig() {
        try {
            const response = await fetch(`${this.guardrailsServiceUrl}/config`, {
                method: 'GET',
                headers: this.getAuthHeaders(),
                timeout: 3000
            });

            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    throw new Error('Authentication failed - check API key');
                }
                throw new Error(`Guardrails service error: ${response.status}`);
            }

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Failed to get config:', error.message);
            return null;
        }
    }
}

// Create singleton instance
const guardrailsValidation = new GuardrailsValidationMiddleware();

module.exports = guardrailsValidation;