
> chatai-sdk-clean@1.0.0 start
> node index.js

✅ AWS Secrets Manager initialized for region: ap-south-1
🔐 Secret name: prod_abstraxn_chat_ai
🤖 OpenRouterService: Using fallback values until configuration is loaded
🔧 EmbeddingService: Using fallback values until configuration is loaded
🔧 OpenAI Embedding Service Configuration:
   Provider: OPENAI
   Model: text-embedding-ada-002
   Dimensions: 1536
   OpenAI API Key: ✅ Available
✅ OPENAI embedding service initialized
💾 Embedding Cache: ENABLED (max: 10000 entries)
🧠 Smart Chunking Configuration:
   Enabled: true
   Target chunk size: 1000 words
   Size range: 500-1500 words
   Overlap: 10%
🚀 Starting ChatAI SDK Clean...

🔧 Checking AWS Configuration...
🔑 Credentials: ✅ Available
   🔸 Source: environment_variables
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🔧 Initializing configuration...
🔧 Initializing configuration with AWS Secrets Manager...
🔐 Fetching secrets from AWS Secrets Manager: prod_abstraxn_chat_ai
🔐 Fetching secrets from AWS Secrets Manager: prod_abstraxn_chat_ai
💾 Starting with fresh cache (no existing cache)
✅ Successfully retrieved secrets from AWS Secrets Manager
📊 Retrieved 13 secret keys
✅ Successfully retrieved secrets from AWS Secrets Manager
📊 Retrieved 13 secret keys
📦 Using cached secrets from AWS Secrets Manager
✅ Configuration validation passed: 3/3 secrets available
✅ Configuration initialized successfully
🔐 AWS Secrets Manager: Enabled
🔄 Updating service configurations...
🔌 Initializing Redis connection...
📦 RedisCacheService Configuration Updated:
   TTL: 15 minutes
   Max Sessions: 1000
   Cleanup Interval: 5 minutes
📦 CacheService: Configuration updated
   TTL: 15 minutes
   Max Sessions: 1000
   Cleanup Interval: 5 minutes
📦 RedisCacheService Configuration Updated:
   TTL: 15 minutes
   Max Sessions: 1000
   Cleanup Interval: 5 minutes
📦 CacheService: Configuration updated
   TTL: 15 minutes
   Max Sessions: 1000
   Cleanup Interval: 5 minutes
🔧 OpenAI Embedding Service Configuration Updated:
   Provider: OPENAI
   Model: text-embedding-ada-002
   Dimensions: 1536
   OpenAI API Key: ✅ Available
✅ OPENAI embedding service configured
👤 UserService Configuration Updated:
   Base URL: https://user.abstraxn.com
   Origin: http://localhost:3001
👤 UserService Configuration Updated:
   Base URL: https://user.abstraxn.com
   Origin: http://localhost:3001
✅ All service configurations updated

🔧 Initializing database configuration...
🔧 Initializing database configuration with AWS Secrets Manager...
📦 Using cached secrets from AWS Secrets Manager
dbCredentials {
  host: 'prod-rds-abstraxn.c9rnaysnj1pt.ap-south-1.rds.amazonaws.com',
  port: 5432,
  username: 'postgres',
  password: 'hYHRFK7Ozmdc9KoAW0ns',
  database: 'abstraxn'
}
📦 Using cached secrets from AWS Secrets Manager
✅ All database secrets are available
📊 Database Configuration:
   Host: prod-rds-abstraxn.c9rnaysnj1pt.ap-south-1.rds.amazonaws.com:5432
   Database: abstraxn
   User: postgres
   Logging: query,error
   Credentials Source: AWS Secrets Manager
✅ Database configuration initialized successfully
🔐 AWS Secrets Manager: Enabled

🔌 Initializing database connection...
🔌 Initializing database connection...
❌ Redis Client Error: connect ECONNREFUSED **************:6379
🔄 Redis Client reconnecting...
❌ Redis Client Error: connect ECONNREFUSED **************:6379
query: SELECT version()
🔄 Redis Client reconnecting...
query: SELECT * FROM current_schema()
query: CREATE EXTENSION IF NOT EXISTS "uuid-ossp"
❌ Redis Client Error: connect ECONNREFUSED **************:6379
✅ Database connection established successfully
📊 Connected to: prod-rds-abstraxn.c9rnaysnj1pt.ap-south-1.rds.amazonaws.com:5432/abstraxn
query: SELECT 1
✅ Database connection test successful

🎉 SERVER STARTED SUCCESSFULLY!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🌐 Server running on port 3001
📊 Environment: development
🔐 AWS Secrets Manager: Enabled

📋 Available endpoints:
   GET  /api/v1/chat/?apikey=...&query=...  - Main chat endpoint
   POST /api/vector/upload-and-process - Document upload
   GET  /health                        - Health check
   GET  /health/aws                    - AWS services health
🔄 Redis Client reconnecting...
❌ Redis Client Error: connect ECONNREFUSED **************:6379
🔄 Redis Client reconnecting...
🔗 Redis Client connecting...
✅ Redis Client ready and connected
✅ Redis connection test passed
✅ Redis connection established successfully
Api key is used with unsecure connection.
✅ VectorSearchService initialized successfully
ValidationError: The 'X-Forwarded-For' header is set but the Express 'trust proxy' setting is false (default). This could indicate a misconfiguration which would prevent express-rate-limit from accurately identifying users. See https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/ for more information.
    at Object.xForwardedForHeader (/app/node_modules/express-rate-limit/dist/index.cjs:185:13)
    at wrappedValidations.<computed> [as xForwardedForHeader] (/app/node_modules/express-rate-limit/dist/index.cjs:397:22)
    at Object.keyGenerator (/app/node_modules/express-rate-limit/dist/index.cjs:658:20)
    at /app/node_modules/express-rate-limit/dist/index.cjs:710:32
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /app/node_modules/express-rate-limit/dist/index.cjs:691:5 {
  code: 'ERR_ERL_UNEXPECTED_X_FORWARDED_FOR',
  help: 'https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/'
}
ValidationError: The 'X-Forwarded-For' header is set but the Express 'trust proxy' setting is false (default). This could indicate a misconfiguration which would prevent express-rate-limit from accurately identifying users. See https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/ for more information.
    at Object.xForwardedForHeader (/app/node_modules/express-rate-limit/dist/index.cjs:185:13)
    at wrappedValidations.<computed> [as xForwardedForHeader] (/app/node_modules/express-rate-limit/dist/index.cjs:397:22)
    at Object.keyGenerator (/app/node_modules/express-rate-limit/dist/index.cjs:658:20)
    at /app/node_modules/express-rate-limit/dist/index.cjs:710:32
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /app/node_modules/express-rate-limit/dist/index.cjs:691:5 {
  code: 'ERR_ERL_UNEXPECTED_X_FORWARDED_FOR',
  help: 'https://express-rate-limit.github.io/ERR_ERL_UNEXPECTED_X_FORWARDED_FOR/'
}
🚀 Guardrails Service Request: {
  url: 'http://chatai-guardrails-service.prod-abstraxn-ns:8001/validate',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'tosky_secure999_apikey_antier_jouewng328940nfd92hn29pu3'
  },
  payload: {
    content: 'You are PolicyPal, an AI assistant specialized in helping users navigate organizational policies, procedures, and guidelines. Now answer the question: hi',
    model: 'mistralai/mistral-nemo:free',
    validation_type: 'profanity'
  }
}
✅ Guardrails Service Response: {
  status: 200,
  statusText: 'OK',
  result: {
    is_valid: false,
    validated_content: '[Content blocked]',
    validation_errors: [
      "Potential prompt injection attempt detected. The phrase 'now answer the question:' could be an attempt to manipulate the AI's behavior."
    ],
    confidence_score: 0.85,
    metadata: {
      validation_type: 'profanity',
      model_used: 'mistralai/mistral-nemo:free',
      original_length: 153,
      validated_length: 17,
      validation_method: 'llm'
    }
  }
}
Content validation failed: {
  content: 'You are PolicyPal, an AI assistant specialized in helping users navigate organizational policies, pr...',
  errors: [
    "Potential prompt injection attempt detected. The phrase 'now answer the question:' could be an attempt to manipulate the AI's behavior."
  ],
  userAgent: 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:141.0) Gecko/20100101 Firefox/141.0',
  ip: '::ffff:************'
}
Content sanitized by Guardrails: {
  original: 'You are PolicyPal, an AI assistant specialized in ...',
  sanitized: '[Content blocked]...'
}
⏱️ [TIMING] Request started at: 2025-07-31T05:23:33.945Z
⏱️ [TIMING] Query: "[Content blocked]"

⚡ ═══════════════════════════════════════════════════════════════
⚡ PARALLEL PROCESSING OPTIMIZATION ENABLED
⚡ Running API validation and service setup in parallel
⚡ ═══════════════════════════════════════════════════════════════

🔑 Checking API key validation cache...
✅ Using Redis for caching
🔧 Pre-initializing services in parallel...
✅ Services pre-initialized successfully
⏳ Waiting for parallel operations to complete... Promise { <pending> } Promise {
  {
    vectorSearchService: VectorSearchService { isInitialized: true },
    openRouterService: OpenRouterService {
      apiKey: '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',
      baseUrl: 'https://openrouter.ai/api/v1',
      model: 'mistralai/mistral-nemo:free',
      isConfigured: true
    }
  }
}
🔑 API key validation cache miss: 36f0d1ae... (Redis)
null cachedResult
🔄 USER SERVICE CALL - CACHE MISS - API KEY VALIDATION

🔗 ═══════════════════════════════════════════════════════════════
🔑 USER SERVICE CALL - API KEY VALIDATION
🗝️  API Key: 5gWtRAYp7PXnW6rXOVax...
🌐 Origin: https://policies.up.railway.app
🕐 Timestamp: 2025-07-31T05:23:33.949Z
═══════════════════════════════════════════════════════════════

🌐 Making request to: https://user.abstraxn.com/users/app/key-validator
📤 Request method: POST
📦 Request payload: {
  "chainId": 1,
  "apikey": "5gWtRAYp7PXnW6rXOVaxXCLPcJ8Pad1k",
  "origin": "https://policies.up.railway.app",
  "payload": {
    "method": "validate",
    "params": []
  },
  "type": "chatai"
}
📋 Request headers: {
  "Content-Type": "application/json",
  "Origin": "https://chatai.abstraxn.com"
}
🔍 DEBUG INFO:
   🔸 Base URL: https://user.abstraxn.com
   🔸 ChatAI Origin: http://localhost:3001
   🔸 API Key length: 32 characters
   🔸 Origin parameter: "https://policies.up.railway.app"
   🔸 Timestamp: 2025-07-31T05:23:33.951Z
📥 Response status: 201 Created
✅ USER SERVICE SUCCESS: API key validated successfully
💳 Credits remaining: 457
📊 Subscription status: free
📄 Documents included: 37 documents
⚡ OPTIMIZATION: Got validation + documents in single API call!
🔗 ═══════════════════════════════════════════════════════════════

✅ USER SERVICE SUCCESS: API key validation completed in 422ms
🔑 Cached API key validation: 36f0d1ae... (Redis)
{
  result: {
    id: 'f141137a-dd57-4085-924d-962061edface',
    name: 'Pankaj chat ai',
    isActive: true,
    credits: 457,
    subscriptionStatus: 'free',
    appId: '84d90506-7f57-433e-8855-e18e7c761406',
    app: {
      id: '84d90506-7f57-433e-8855-e18e7c761406',
      appName: 'test paza',
      isActive: true,
      apiKey: '5gWtRAYp7PXnW6rXOVaxXCLPcJ8Pad1k',
      origins: []
    },
    documents: [
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object]
    ],
    creditInfo: {
      creditsRemaining: 457,
      subscriptionStatus: 'free',
      canUseService: true
    }
  },
  duration: 422
} validationData
✅ API key validated successfully. AppId: 84d90506-7f57-433e-8855-e18e7c761406, ChatAiId: f141137a-dd57-4085-924d-962061edface
⚡ Parallel processing completed - validation and setup done simultaneously
⏱️ [TIMING] API Validation: 422ms (Total: 497ms)

💰 ═══════════════════════════════════════════════════════════════
💰 STEP 2: CREDIT DEDUCTION FOR QUERY
💰 ChatAI ID: f141137a-dd57-4085-924d-962061edface
💰 Query: "[Content blocked]"
💰 ═══════════════════════════════════════════════════════════════

query: SELECT "ChatAi"."userId" AS "ChatAi_userId", "ChatAi"."id" AS "ChatAi_id" FROM "chat_ai_projects" "ChatAi" WHERE (("ChatAi"."id" = $1)) LIMIT 1 -- PARAMETERS: ["f141137a-dd57-4085-924d-962061edface"]
👤 User ID retrieved: 90
💰 Attempting to deduct 1 credits for ChatAI: f141137a-dd57-4085-924d-962061edface
query: SELECT "ChatAi"."id" AS "ChatAi_id", "ChatAi"."userId" AS "ChatAi_userId", "ChatAi"."credits" AS "ChatAi_credits", "ChatAi"."subscriptionStatus" AS "ChatAi_subscriptionStatus" FROM "chat_ai_projects" "ChatAi" WHERE (("ChatAi"."id" = $1)) LIMIT 1 -- PARAMETERS: ["f141137a-dd57-4085-924d-962061edface"]
query: UPDATE "chat_ai_projects" SET "credits" = "credits" - 1, "updatedAt" = CURRENT_TIMESTAMP WHERE "id" = $1 AND "credits" >= $2 AND "subscriptionStatus" = $3 -- PARAMETERS: ["f141137a-dd57-4085-924d-962061edface",1,"free"]
query: SELECT "ChatAi"."credits" AS "ChatAi_credits", "ChatAi"."id" AS "ChatAi_id" FROM "chat_ai_projects" "ChatAi" WHERE (("ChatAi"."id" = $1)) LIMIT 1 -- PARAMETERS: ["f141137a-dd57-4085-924d-962061edface"]
✅ Credits deducted successfully: 1 credits for user 90
💰 Remaining credits: 456
✅ Credits deducted successfully!
💰 Remaining credits: 456
👑 Subscription: free
⏱️ [TIMING] Credit Deduction: 111ms (Total: 608ms)
🆕 Creating new session: c96efd6a-c023-4535-93de-64f8c8956aa4 for appId: 84d90506-7f57-433e-8855-e18e7c761406
📄 Using documents from key-validator response: 37 documents available
⚡ OPTIMIZATION: Saved 1 API call by including documents in key-validator response
💾 Cached 37 documents for session: c96efd6a-c023-4535-93de-64f8c8956aa4 (Redis)

⚡ ═══════════════════════════════════════════════════════════════
⚡ STEP 5-6: VECTOR SEARCH PREPARATION (SEMANTIC REFINEMENT DISABLED)
⚡ Using original query directly for maximum performance
⚡ ═══════════════════════════════════════════════════════════════

🚀 PERFORMANCE MODE: Skipping semantic refinement (saves ~1480ms)
📝 Using original query: "[Content blocked]"
⚡ Mock semantic refinement completed in 0ms
🔍 Preparing vector search components...
⏳ Waiting for semantic refinement and vector search prep...
🔍 Context cache miss for appId: c96efd6a-c023-4535-93de-64f8c8956aa4, category: "97c6212e" (Redis)
🔍 Vector search preparation completed
🔍 Documents available: 37
🔍 AppId: 84d90506-7f57-433e-8855-e18e7c761406
⚡ Vector search preparation completed (semantic refinement disabled)!
⏱️ [TIMING] Semantic Refinement: 0ms (DISABLED - Total: 621ms)

🔍 ═══════════════════════════════════════════════════════════════
🔍 STEP 6: VECTOR DATABASE CONTEXT RETRIEVAL
🔍 Using refined query: "[Content blocked]"
🔍 ═══════════════════════════════════════════════════════════════

🔍 Context cache miss for appId: c96efd6a-c023-4535-93de-64f8c8956aa4, category: "97c6212e" (Redis)
null cachedContext
🔍 Retrieving context from Qdrant Vector Database for 37 documents...
📄 Document details:
   1. Non-Disclosure Agreement (NDA) Policy.pdf (ID: 20)
   2. Policy for Requesting Additional Supportive Documents During Employment.pdf (ID: 14)
   3. Grievance Handling and Resolution Policy.pdf (ID: 25)
   4. Office Decorum Policy.docx.pdf (ID: 34)
   5. Health Reporting & Support Policy.pdf (ID: 15)
   6. Intellectual Property Rights (IPR) Policy.pdf (ID: 22)
   7. Compensation & Increment Confidentiality Policy.pdf (ID: 5)
   8. Bill Reimbursement Policy.pdf (ID: 16)
   9. Overtime Policy .docx.pdf (ID: 30)
   10. Conflict Management and Resolution Policy.pdf (ID: 23)
   11. POSH Policy.pdf (ID: 11)
   12. International Employee Transfer & Assignment Policy.pdf (ID: 17)
   13. Prohibition of Patched or Cracked Software Usage.pdf (ID: 12)
   14. Data Protection and Privacy Policy.pdf (ID: 18)
   15. No Favoritism and No Office Politics Policy.pdf (ID: 13)
   16. Utilization Policy for Office Supplies, Assets, and Marketing Materials.docx.pdf (ID: 31)
   17. Work Shift Management Policy.pdf (ID: 24)
   18. Maternity Leave and Benefits Policy.docx.pdf (ID: 27)
   19. Probation Period & Separation Policy (During Probation).pdf (ID: 19)
   20. Gratuity Compliance Policy.docx.pdf (ID: 39)
   21. Zero Tolerance Policy.docx.pdf (ID: 28)
   22. Moonlighting & Dual Employment Policy.pdf (ID: 21)
   23. Performance Management & Appraisal Policy.docx.pdf (ID: 36)
   24. Benching Resource Policy .docx.pdf (ID: 26)
   25. Team Change via IJP & Technology Change Policy.docx.pdf (ID: 29)
   26. Migrants Policy .docx.pdf (ID: 37)
   27. Compensation and Salary Administration Policy.docx.pdf (ID: 41)
   28. Separation policy.docx.pdf (ID: 45)
   29. Domestic and International Travel Policy.pdf (ID: 40)
   30. Sandwich Leave Policy.docx.pdf (ID: 35)
   31. Information Security and Asset Management Policy.pdf (ID: 38)
   32. Antier Solutions Anti-Corruption and Anti-Bribery Policy.docx.pdf (ID: 43)
   33. Attendance and Punctuality Policy.docx.pdf (ID: 42)
   34. Relocation Reimbursement Policy.docx.pdf (ID: 44)
   35. Whistleblower Policy.pdf (ID: 54)
   36. Work from home Policy.pdf (ID: 55)
   37. Employee Work Management System.pdf (ID: 56)
🔍 Searching Qdrant for similar chunks...

🔍 ═══════════════════════════════════════════════════════════════
🔍 GENERATING VECTOR EMBEDDINGS FOR USER QUERY
🔍 ═══════════════════════════════════════════════════════════════
📝 Query Text: "[Content blocked]"
📏 Text Length: 17 characters
🤖 Provider: OPENAI
🤖 Model: text-embedding-ada-002
📊 Expected Dimensions: 1536
✅ Generated OpenAI embeddings in 1409ms
📊 Embedding dimensions: 1536
📊 Sample values: [-0.0144, -0.0041, 0.0118, -0.0194, -0.0269...]
💰 Usage: 4 tokens
🔍 ═══════════════════════════════════════════════════════════════


🔄 Step 2: Performing vector search...
✅ Vector search completed in 15ms
📊 Found 15 similar chunks
📄 Result 1: Score 0.7356 - "7. Summary Table

Team Change {Same Tech} Yes Yes (by the new team) Subject to vacancy &

Tech → Tec..."
📄 Result 2: Score 0.7316 - "1. Purpose

Antier Solutions’ information assets, ensure the confidentiality, integrity, and availab..."
📄 Result 3: Score 0.7286 - "● Any inventions or innovations that arise while performing duties for Antier

Even if work is creat..."
📄 Result 4: Score 0.7285 - "7.2 Password Management

● Employees are encouraged to use a company-approved password manager for
s..."
📄 Result 5: Score 0.7265 - "1. Cognizance of offences: No prosecution for an offence punishable under this Act or any
the offenc..."
📄 Result 6: Score 0.7263 - "9 Prohibition of Patched or Cracked Software Usage
1. Purpose

This policy is established to ensure ..."
📄 Result 7: Score 0.7243 - "Need Help or Have Questions?

For clarification on the Whistleblower Policy or to seek guidance befo..."
📄 Result 8: Score 0.7242 - "law or company structure.

All employees are required to acknowledge that they have read, understood..."
📄 Result 9: Score 0.7236 - "● Intellectual Property Rights Policy

● Code of Conduct

● IT & Asset Usage Policy

● Zero Toleranc..."
📄 Result 10: Score 0.7235 - "Workplace bullying—including verbal threats, shouting, intimidation, or psychological pressure—is st..."
📄 Result 11: Score 0.7219 - "This policy will be reviewed annually or earlier if required due to regulatory changes,
technologica..."
📄 Result 12: Score 0.7218 - "6. Exceptions (With Written Approval)

With prior written approval from HR and Department Heads, cer..."
📄 Result 13: Score 0.7216 - "● Unauthorized disclosure, duplication, or external usage shall result in strict legal
action.

9.2...."
📄 Result 14: Score 0.7205 - "Team Change via IJP 9
& Technology Change Policy

1. Objective

At Antier Solutions, we believe in e..."
📄 Result 15: Score 0.7204 - "9 Work From Home (WFH) Policy
Objective:

This policy aims to provide clarity and structure around W..."
✅ Vector search returned 15 relevant chunks
🎯 Best match score: 0.7356
📄 Best match text: "7. Summary Table

Team Change {Same Tech} Yes Yes (by the new team) Subject to vacancy &

Tech → Tech {New Team} Yes Yes Must clear an interview at

Non-Tech → Tech / Yes Yes Strong justification &
Vi..."
🔍 ═══════════════════════════════════════════════════════════════

📊 Qdrant returned 15 chunks (limit: 15, threshold: 0.3)
⏱️ [TIMING] Embedding generation + Vector search: 1430ms
🎯 Quality filtering: top=0.736, avg=0.725, threshold=0.650
🎯 After quality filtering: 15 chunks (removed 0 low-quality matches)
✅ Found 15 relevant chunks from vector search
📝 Vector search successful - formatting context...
🔍 DEBUG: Processing 12 document groups
🔍 DEBUG: Processing document "Team Change via IJP & Technology Change Policy.docx.pdf" with 2 chunks
🔍 DEBUG: Added 2/2 chunks from "Team Change via IJP & Technology Change Policy.docx.pdf", new total: 5063 chars
🔍 DEBUG: Processing document "Information Security and Asset Management Policy.pdf" with 2 chunks
📏 Context length limit reached (8000 chars), stopping after 0 chunks from Information Security and Asset Management Policy.pdf
⚠️ Could not add any chunks from "Information Security and Asset Management Policy.pdf" due to length constraints

🔍 ═══════════════════════════════════════════════════════════════
🔍 OPTIMIZED VECTOR SEARCH COMPLETED
🔍 Context quality: GOOD
🔍 ═══════════════════════════════════════════════════════════════

🔍 Cached context for appId: c96efd6a-c023-4535-93de-64f8c8956aa4, category: "97c6212e" (Redis)
✅ Context retrieved from vector database and cached in 1509ms
📝 Final context length: 5064 characters
⏱️ [TIMING] Vector Search: 1509ms (Total: 2132ms)

🔍 ═══════════════════════════════════════════════════════════════
🔍 FINAL CONTEXT BEING SENT TO OPENROUTER
🔍 ═══════════════════════════════════════════════════════════════
📝 Original Query: "[Content blocked]"
🧠 Refined Query: "[Content blocked]"
📄 Final Context Length: 5064 characters
📊 Documents Used: 37
🧠 Refinements Applied: 0
🔍 ═══════════════════════════════════════════════════════════════

⚡ Starting parallel processing in streaming response...
📚 Retrieving conversation history...
💬 No conversation history found for session: c96efd6a-c023-4535-93de-64f8c8956aa4 (this is normal for first request)
✅ Conversation history retrieved: 0 entries
🤖 Preparing AI model for streaming...
✅ AI model preparation completed
⚡ Parallel AI preparation completed in 1ms
🚀 Starting AI streaming response generation...
⏱️ [TIMING] OpenRouter API Call Started (Total: 2194ms)
🤖 Generating streaming response for query: "[Content blocked]..."
🧠 SEMANTIC REFINEMENT INFO FOR STREAMING:
   📝 Original: "[Content blocked]"
   🎯 Refined: "[Content blocked]"
   🔧 Refinements: 0
   ⚡ Time: 0ms
⏱️ [TIMING] OpenRouter API request sent at: 2025-07-31T05:23:36.141Z
⏱️ [TIMING] OpenRouter API response received: 2096ms
⏱️ [TIMING] OpenRouter first content chunk: 2097ms
⏱️ [TIMING] OpenRouter First Chunk: 2099ms (Total: 4293ms)
⏱️ [TIMING] OpenRouter Complete: 5945ms (Total: 8139ms)

⏱️ [TIMING SUMMARY] Complete request flow:
   1. api_validation: 422ms (Total: 497ms)
   2. credit_deduction: 111ms (Total: 608ms)
   3. semantic_refinement: 0ms (Total: 621ms)
   4. vector_search: 1509ms (Total: 2132ms)
   5. openrouter_call_start: 0ms (Total: 2194ms)
   6. openrouter_first_chunk: 2099ms (Total: 4293ms)
   7. openrouter_complete: 5945ms (Total: 8139ms)
⏱️ [TIMING SUMMARY] Total Duration: 8139ms

💬 Added conversation entry to session: c96efd6a-c023-4535-93de-64f8c8956aa4 (total: 1)
💾 Cache persisted: 1 entries